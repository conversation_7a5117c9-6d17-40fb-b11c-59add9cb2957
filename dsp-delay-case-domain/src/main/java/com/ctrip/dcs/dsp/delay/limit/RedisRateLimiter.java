package com.ctrip.dcs.dsp.delay.limit;

/**
 * Redis限流器接口
 * 提供基于Redis的分布式限流功能
 * 
 * <AUTHOR>
 */
public interface RedisRateLimiter {

    /**
     * 尝试获取许可
     * 
     * @param key 限流key
     * @param permits 请求的许可数量，默认为1
     * @return 是否成功获取许可
     */
    boolean tryAcquire(String key, int permits);

    /**
     * 尝试获取单个许可
     * 
     * @param key 限流key
     * @return 是否成功获取许可
     */
    default boolean tryAcquire(String key) {
        return tryAcquire(key, 1);
    }

    /**
     * 尝试获取许可，使用指定的限流配置
     * 
     * @param key 限流key
     * @param permits 请求的许可数量
     * @param windowSizeSeconds 窗口大小（秒）
     * @param limit 限流阈值
     * @return 是否成功获取许可
     */
    boolean tryAcquire(String key, int permits, int windowSizeSeconds, int limit);

    /**
     * 获取剩余许可数量
     * 
     * @param key 限流key
     * @return 剩余许可数量，如果无法获取则返回-1
     */
    long getRemaining(String key);

    /**
     * 获取剩余许可数量，使用指定的限流配置
     * 
     * @param key 限流key
     * @param windowSizeSeconds 窗口大小（秒）
     * @param limit 限流阈值
     * @return 剩余许可数量，如果无法获取则返回-1
     */
    long getRemaining(String key, int windowSizeSeconds, int limit);

    /**
     * 获取当前窗口内的请求计数
     * 
     * @param key 限流key
     * @return 当前窗口内的请求计数
     */
    long getCurrentCount(String key);

    /**
     * 获取当前窗口内的请求计数，使用指定的限流配置
     * 
     * @param key 限流key
     * @param windowSizeSeconds 窗口大小（秒）
     * @return 当前窗口内的请求计数
     */
    long getCurrentCount(String key, int windowSizeSeconds);

    /**
     * 重置限流计数器
     * 
     * @param key 限流key
     * @return 是否成功重置
     */
    boolean reset(String key);

    /**
     * 获取下次重置时间（Unix时间戳，秒）
     * 
     * @param key 限流key
     * @return 下次重置时间，如果无法获取则返回-1
     */
    long getResetTime(String key);

    /**
     * 获取下次重置时间，使用指定的限流配置
     * 
     * @param key 限流key
     * @param windowSizeSeconds 窗口大小（秒）
     * @return 下次重置时间，如果无法获取则返回-1
     */
    long getResetTime(String key, int windowSizeSeconds);

    /**
     * 检查限流器是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 限流结果类
     */
    class RateLimitResult {
        private final boolean allowed;
        private final long remaining;
        private final long resetTime;
        private final long currentCount;

        public RateLimitResult(boolean allowed, long remaining, long resetTime, long currentCount) {
            this.allowed = allowed;
            this.remaining = remaining;
            this.resetTime = resetTime;
            this.currentCount = currentCount;
        }

        public boolean isAllowed() {
            return allowed;
        }

        public long getRemaining() {
            return remaining;
        }

        public long getResetTime() {
            return resetTime;
        }

        public long getCurrentCount() {
            return currentCount;
        }

        @Override
        public String toString() {
            return "RateLimitResult{" +
                    "allowed=" + allowed +
                    ", remaining=" + remaining +
                    ", resetTime=" + resetTime +
                    ", currentCount=" + currentCount +
                    '}';
        }
    }

    /**
     * 尝试获取许可并返回详细结果
     * 
     * @param key 限流key
     * @param permits 请求的许可数量
     * @return 限流结果
     */
    RateLimitResult tryAcquireWithResult(String key, int permits);

    /**
     * 尝试获取许可并返回详细结果，使用指定的限流配置
     * 
     * @param key 限流key
     * @param permits 请求的许可数量
     * @param windowSizeSeconds 窗口大小（秒）
     * @param limit 限流阈值
     * @return 限流结果
     */
    RateLimitResult tryAcquireWithResult(String key, int permits, int windowSizeSeconds, int limit);
}
