package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/**
 * Redis限流器配置类
 * 通过配置文件配置Redis限流器的各种参数
 * 
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("redis_rate_limiter.properties")
public class RedisRateLimiterConfig {

    /**
     * Redis key前缀，默认为 "rate_limiter:"
     */
    private String keyPrefix = "rate_limiter:";

    /**
     * 默认限流窗口大小（秒），默认为60秒
     */
    private Integer defaultWindowSizeSeconds = 60;

    /**
     * 默认限流阈值，默认为100次/窗口
     */
    private Integer defaultLimit = 100;

    /**
     * 默认过期时间（秒），默认为窗口大小的2倍
     */
    private Integer defaultExpireSeconds = 120;

    /**
     * API限流配置 - 窗口大小（秒）
     */
    private Integer apiWindowSizeSeconds = 60;

    /**
     * API限流配置 - 限流阈值
     */
    private Integer apiLimit = 1000;

    /**
     * API限流配置 - 过期时间（秒）
     */
    private Integer apiExpireSeconds = 120;

    /**
     * 用户限流配置 - 窗口大小（秒）
     */
    private Integer userWindowSizeSeconds = 60;

    /**
     * 用户限流配置 - 限流阈值
     */
    private Integer userLimit = 100;

    /**
     * 用户限流配置 - 过期时间（秒）
     */
    private Integer userExpireSeconds = 120;

    /**
     * IP限流配置 - 窗口大小（秒）
     */
    private Integer ipWindowSizeSeconds = 60;

    /**
     * IP限流配置 - 限流阈值
     */
    private Integer ipLimit = 500;

    /**
     * IP限流配置 - 过期时间（秒）
     */
    private Integer ipExpireSeconds = 120;

    /**
     * 订单限流配置 - 窗口大小（秒）
     */
    private Integer orderWindowSizeSeconds = 60;

    /**
     * 订单限流配置 - 限流阈值
     */
    private Integer orderLimit = 200;

    /**
     * 订单限流配置 - 过期时间（秒）
     */
    private Integer orderExpireSeconds = 120;

    /**
     * 司机限流配置 - 窗口大小（秒）
     */
    private Integer driverWindowSizeSeconds = 60;

    /**
     * 司机限流配置 - 限流阈值
     */
    private Integer driverLimit = 50;

    /**
     * 司机限流配置 - 过期时间（秒）
     */
    private Integer driverExpireSeconds = 120;

    /**
     * 调度限流配置 - 窗口大小（秒）
     */
    private Integer dispatchWindowSizeSeconds = 60;

    /**
     * 调度限流配置 - 限流阈值
     */
    private Integer dispatchLimit = 300;

    /**
     * 调度限流配置 - 过期时间（秒）
     */
    private Integer dispatchExpireSeconds = 120;

    /**
     * 是否启用Redis限流器，默认启用
     */
    private Boolean enabled = true;

    /**
     * 是否启用调试模式，默认关闭
     */
    private Boolean debugMode = false;

    /**
     * 限流器类型：sliding_window（滑动窗口）或 fixed_window（固定窗口），默认为滑动窗口
     */
    private String limiterType = "sliding_window";

    /**
     * 滑动窗口分片数量，默认为10个分片
     */
    private Integer slidingWindowShards = 10;

    /**
     * Redis操作超时时间（毫秒），默认为1000ms
     */
    private Integer redisTimeoutMs = 1000;

    /**
     * 获取API限流配置
     */
    public RateLimiterConfigItem getApiConfig() {
        return new RateLimiterConfigItem(apiWindowSizeSeconds, apiLimit, apiExpireSeconds);
    }

    /**
     * 获取用户限流配置
     */
    public RateLimiterConfigItem getUserConfig() {
        return new RateLimiterConfigItem(userWindowSizeSeconds, userLimit, userExpireSeconds);
    }

    /**
     * 获取IP限流配置
     */
    public RateLimiterConfigItem getIpConfig() {
        return new RateLimiterConfigItem(ipWindowSizeSeconds, ipLimit, ipExpireSeconds);
    }

    /**
     * 获取订单限流配置
     */
    public RateLimiterConfigItem getOrderConfig() {
        return new RateLimiterConfigItem(orderWindowSizeSeconds, orderLimit, orderExpireSeconds);
    }

    /**
     * 获取司机限流配置
     */
    public RateLimiterConfigItem getDriverConfig() {
        return new RateLimiterConfigItem(driverWindowSizeSeconds, driverLimit, driverExpireSeconds);
    }

    /**
     * 获取调度限流配置
     */
    public RateLimiterConfigItem getDispatchConfig() {
        return new RateLimiterConfigItem(dispatchWindowSizeSeconds, dispatchLimit, dispatchExpireSeconds);
    }

    /**
     * 获取默认限流配置
     */
    public RateLimiterConfigItem getDefaultConfig() {
        return new RateLimiterConfigItem(defaultWindowSizeSeconds, defaultLimit, defaultExpireSeconds);
    }

    /**
     * 限流配置项
     */
    @Getter
    @Setter
    public static class RateLimiterConfigItem {
        private Integer windowSizeSeconds;
        private Integer limit;
        private Integer expireSeconds;

        public RateLimiterConfigItem(Integer windowSizeSeconds, Integer limit, Integer expireSeconds) {
            this.windowSizeSeconds = windowSizeSeconds;
            this.limit = limit;
            this.expireSeconds = expireSeconds;
        }
    }
}
