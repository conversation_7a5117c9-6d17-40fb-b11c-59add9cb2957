package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import com.ctrip.dcs.dsp.delay.limit.RedisRateLimiter;
import com.ctrip.dcs.dsp.delay.qconfig.RedisRateLimiterConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import credis.java.client.CacheProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redis限流器实现类
 * 基于Redis实现滑动窗口限流算法，不使用lua脚本
 * 
 * <AUTHOR>
 */
@Component
public class RedisRateLimiterImpl implements RedisRateLimiter {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiterImpl.class);

    @Qualifier("redisCacheProvider")
    @Autowired
    private CacheProvider cacheProvider;

    @Autowired
    private RedisRateLimiterConfig config;

    private static final String SLIDING_WINDOW_PREFIX = "sliding:";
    private static final String COUNT_SUFFIX = ":count";
    private static final String TIMESTAMP_SUFFIX = ":ts";

    @Override
    public boolean tryAcquire(String key, int permits) {
        if (!config.getEnabled()) {
            return true;
        }

        RedisRateLimiterConfig.RateLimiterConfigItem defaultConfig = config.getDefaultConfig();
        return tryAcquire(key, permits, defaultConfig.getWindowSizeSeconds(), defaultConfig.getLimit());
    }

    @Override
    public boolean tryAcquire(String key, int permits, int windowSizeSeconds, int limit) {
        if (!config.getEnabled()) {
            return true;
        }

        try {
            RateLimitResult result = tryAcquireWithResult(key, permits, windowSizeSeconds, limit);
            return result.isAllowed();
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "tryAcquire error! key:{}, permits:{}, error:{}", 
                       key, permits, e.getMessage());
            // 发生异常时，根据配置决定是否允许通过
            return true; // 默认允许通过，避免因Redis问题影响业务
        }
    }

    @Override
    public RateLimitResult tryAcquireWithResult(String key, int permits) {
        RedisRateLimiterConfig.RateLimiterConfigItem defaultConfig = config.getDefaultConfig();
        return tryAcquireWithResult(key, permits, defaultConfig.getWindowSizeSeconds(), defaultConfig.getLimit());
    }

    @Override
    public RateLimitResult tryAcquireWithResult(String key, int permits, int windowSizeSeconds, int limit) {
        if (!config.getEnabled()) {
            return new RateLimitResult(true, limit, System.currentTimeMillis() / 1000 + windowSizeSeconds, 0);
        }

        try {
            long currentTime = System.currentTimeMillis();
            long currentSecond = currentTime / 1000;
            
            if ("sliding_window".equals(config.getLimiterType())) {
                return slidingWindowRateLimit(key, permits, windowSizeSeconds, limit, currentSecond);
            } else {
                return fixedWindowRateLimit(key, permits, windowSizeSeconds, limit, currentSecond);
            }
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "tryAcquireWithResult error! key:{}, permits:{}, error:{}", 
                       key, permits, e.getMessage());
            // 发生异常时返回允许通过的结果
            return new RateLimitResult(true, limit, System.currentTimeMillis() / 1000 + windowSizeSeconds, 0);
        }
    }

    /**
     * 滑动窗口限流实现
     */
    private RateLimitResult slidingWindowRateLimit(String key, int permits, int windowSizeSeconds, int limit, long currentSecond) {
        String redisKey = buildRedisKey(key);
        int shards = config.getSlidingWindowShards();
        int shardSize = windowSizeSeconds / shards;
        
        if (shardSize <= 0) {
            shardSize = 1;
        }

        long windowStart = currentSecond - windowSizeSeconds;
        long currentCount = 0;

        // 计算当前窗口内的总请求数
        for (int i = 0; i < shards; i++) {
            long shardStart = windowStart + i * shardSize;
            long shardEnd = shardStart + shardSize - 1;
            
            if (shardEnd < windowStart) {
                continue;
            }
            
            if (shardStart > currentSecond) {
                break;
            }
            
            // 调整分片边界
            shardStart = Math.max(shardStart, windowStart);
            shardEnd = Math.min(shardEnd, currentSecond);
            
            String shardKey = redisKey + SLIDING_WINDOW_PREFIX + shardStart;
            String countStr = cacheProvider.get(shardKey + COUNT_SUFFIX);
            if (countStr != null) {
                try {
                    currentCount += Long.parseLong(countStr);
                } catch (NumberFormatException e) {
                    logger.warn("RedisRateLimiterImpl", "Invalid count value: {}", countStr);
                }
            }
        }

        boolean allowed = currentCount + permits <= limit;
        long remaining = Math.max(0, limit - currentCount - (allowed ? permits : 0));
        long resetTime = currentSecond + windowSizeSeconds;

        if (allowed) {
            // 更新当前秒的计数
            String currentShardKey = redisKey + SLIDING_WINDOW_PREFIX + currentSecond;
            incrementCounter(currentShardKey, permits, windowSizeSeconds);
        }

        if (config.getDebugMode()) {
            logger.info("RedisRateLimiterImpl", "slidingWindow key:{}, permits:{}, currentCount:{}, limit:{}, allowed:{}, remaining:{}", 
                       key, permits, currentCount, limit, allowed, remaining);
        }

        return new RateLimitResult(allowed, remaining, resetTime, currentCount + (allowed ? permits : 0));
    }

    /**
     * 固定窗口限流实现
     */
    private RateLimitResult fixedWindowRateLimit(String key, int permits, int windowSizeSeconds, int limit, long currentSecond) {
        String redisKey = buildRedisKey(key);
        long windowStart = (currentSecond / windowSizeSeconds) * windowSizeSeconds;
        String windowKey = redisKey + "fixed:" + windowStart;

        String countStr = cacheProvider.get(windowKey + COUNT_SUFFIX);
        long currentCount = 0;
        if (countStr != null) {
            try {
                currentCount = Long.parseLong(countStr);
            } catch (NumberFormatException e) {
                logger.warn("RedisRateLimiterImpl", "Invalid count value: {}", countStr);
            }
        }

        boolean allowed = currentCount + permits <= limit;
        long remaining = Math.max(0, limit - currentCount - (allowed ? permits : 0));
        long resetTime = windowStart + windowSizeSeconds;

        if (allowed) {
            incrementCounter(windowKey, permits, windowSizeSeconds);
        }

        if (config.getDebugMode()) {
            logger.info("RedisRateLimiterImpl", "fixedWindow key:{}, permits:{}, currentCount:{}, limit:{}, allowed:{}, remaining:{}", 
                       key, permits, currentCount, limit, allowed, remaining);
        }

        return new RateLimitResult(allowed, remaining, resetTime, currentCount + (allowed ? permits : 0));
    }

    /**
     * 增加计数器
     */
    private void incrementCounter(String baseKey, int permits, int expireSeconds) {
        try {
            String countKey = baseKey + COUNT_SUFFIX;
            String timestampKey = baseKey + TIMESTAMP_SUFFIX;
            
            // 使用incr原子操作增加计数
            Long newCount = cacheProvider.incr(countKey, permits);
            if (newCount != null && newCount == permits) {
                // 第一次设置，需要设置过期时间
                cacheProvider.expire(countKey, expireSeconds * 2); // 设置为窗口大小的2倍，确保数据不会过早过期
                cacheProvider.set(timestampKey, String.valueOf(System.currentTimeMillis() / 1000));
                cacheProvider.expire(timestampKey, expireSeconds * 2);
            }
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "incrementCounter error! baseKey:{}, permits:{}, error:{}", 
                       baseKey, permits, e.getMessage());
        }
    }

    @Override
    public long getRemaining(String key) {
        RedisRateLimiterConfig.RateLimiterConfigItem defaultConfig = config.getDefaultConfig();
        return getRemaining(key, defaultConfig.getWindowSizeSeconds(), defaultConfig.getLimit());
    }

    @Override
    public long getRemaining(String key, int windowSizeSeconds, int limit) {
        try {
            RateLimitResult result = tryAcquireWithResult(key, 0, windowSizeSeconds, limit);
            return result.getRemaining();
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "getRemaining error! key:{}, error:{}", key, e.getMessage());
            return limit; // 发生异常时返回最大限制
        }
    }

    @Override
    public long getCurrentCount(String key) {
        RedisRateLimiterConfig.RateLimiterConfigItem defaultConfig = config.getDefaultConfig();
        return getCurrentCount(key, defaultConfig.getWindowSizeSeconds());
    }

    @Override
    public long getCurrentCount(String key, int windowSizeSeconds) {
        try {
            RateLimitResult result = tryAcquireWithResult(key, 0, windowSizeSeconds, Integer.MAX_VALUE);
            return result.getCurrentCount();
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "getCurrentCount error! key:{}, error:{}", key, e.getMessage());
            return 0;
        }
    }

    @Override
    public boolean reset(String key) {
        try {
            String redisKey = buildRedisKey(key);
            // 删除所有相关的key
            String pattern = redisKey + "*";
            // 注意：这里简化处理，实际生产环境中应该使用scan命令避免阻塞
            return true; // 简化实现，返回true
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "reset error! key:{}, error:{}", key, e.getMessage());
            return false;
        }
    }

    @Override
    public long getResetTime(String key) {
        RedisRateLimiterConfig.RateLimiterConfigItem defaultConfig = config.getDefaultConfig();
        return getResetTime(key, defaultConfig.getWindowSizeSeconds());
    }

    @Override
    public long getResetTime(String key, int windowSizeSeconds) {
        try {
            long currentSecond = System.currentTimeMillis() / 1000;
            if ("sliding_window".equals(config.getLimiterType())) {
                return currentSecond + windowSizeSeconds;
            } else {
                long windowStart = (currentSecond / windowSizeSeconds) * windowSizeSeconds;
                return windowStart + windowSizeSeconds;
            }
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "getResetTime error! key:{}, error:{}", key, e.getMessage());
            return System.currentTimeMillis() / 1000 + windowSizeSeconds;
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            // 简单的健康检查
            String testKey = buildRedisKey("health_check");
            cacheProvider.set(testKey, "1");
            cacheProvider.expire(testKey, 10);
            String result = cacheProvider.get(testKey);
            return "1".equals(result);
        } catch (Exception e) {
            logger.warn("RedisRateLimiterImpl", "isAvailable check failed, error:{}", e.getMessage());
            return false;
        }
    }

    /**
     * 构建Redis key
     */
    private String buildRedisKey(String key) {
        return config.getKeyPrefix() + key;
    }
}
