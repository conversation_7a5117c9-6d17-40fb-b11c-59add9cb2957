package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import com.ctrip.dcs.dsp.delay.limit.RedisRateLimiter;
import com.ctrip.dcs.dsp.delay.qconfig.RedisRateLimiterConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redis限流器配置类
 * 配置Redis限流器相关的Spring Bean
 * 
 * <AUTHOR>
 */
@Configuration
public class RedisRateLimiterConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiterConfiguration.class);

    @Autowired
    private RedisRateLimiterConfig redisRateLimiterConfig;

    /**
     * 创建Redis限流器Bean
     */
    @Bean("redisRateLimiter")
    public RedisRateLimiter redisRateLimiter() {
        logger.info("RedisRateLimiterConfiguration", "Creating RedisRateLimiter bean with config: enabled={}, limiterType={}, keyPrefix={}", 
                   redisRateLimiterConfig.getEnabled(), 
                   redisRateLimiterConfig.getLimiterType(), 
                   redisRateLimiterConfig.getKeyPrefix());
        
        return new RedisRateLimiterImpl();
    }

    /**
     * 创建API限流器Bean
     */
    @Bean("apiRateLimiter")
    public ApiRateLimiter apiRateLimiter(RedisRateLimiter redisRateLimiter) {
        return new ApiRateLimiter(redisRateLimiter, redisRateLimiterConfig);
    }

    /**
     * 创建用户限流器Bean
     */
    @Bean("userRateLimiter")
    public UserRateLimiter userRateLimiter(RedisRateLimiter redisRateLimiter) {
        return new UserRateLimiter(redisRateLimiter, redisRateLimiterConfig);
    }

    /**
     * 创建IP限流器Bean
     */
    @Bean("ipRateLimiter")
    public IpRateLimiter ipRateLimiter(RedisRateLimiter redisRateLimiter) {
        return new IpRateLimiter(redisRateLimiter, redisRateLimiterConfig);
    }

    /**
     * 创建订单限流器Bean
     */
    @Bean("orderRateLimiter")
    public OrderRateLimiter orderRateLimiter(RedisRateLimiter redisRateLimiter) {
        return new OrderRateLimiter(redisRateLimiter, redisRateLimiterConfig);
    }

    /**
     * 创建司机限流器Bean
     */
    @Bean("driverRateLimiter")
    public DriverRateLimiter driverRateLimiter(RedisRateLimiter redisRateLimiter) {
        return new DriverRateLimiter(redisRateLimiter, redisRateLimiterConfig);
    }

    /**
     * 创建调度限流器Bean
     */
    @Bean("dispatchRateLimiter")
    public DispatchRateLimiter dispatchRateLimiter(RedisRateLimiter redisRateLimiter) {
        return new DispatchRateLimiter(redisRateLimiter, redisRateLimiterConfig);
    }

    /**
     * API限流器
     */
    public static class ApiRateLimiter {
        private final RedisRateLimiter redisRateLimiter;
        private final RedisRateLimiterConfig config;

        public ApiRateLimiter(RedisRateLimiter redisRateLimiter, RedisRateLimiterConfig config) {
            this.redisRateLimiter = redisRateLimiter;
            this.config = config;
        }

        public boolean tryAcquire(String apiKey) {
            RedisRateLimiterConfig.RateLimiterConfigItem apiConfig = config.getApiConfig();
            return redisRateLimiter.tryAcquire("api:" + apiKey, 1, 
                                             apiConfig.getWindowSizeSeconds(), 
                                             apiConfig.getLimit());
        }

        public RedisRateLimiter.RateLimitResult tryAcquireWithResult(String apiKey) {
            RedisRateLimiterConfig.RateLimiterConfigItem apiConfig = config.getApiConfig();
            return redisRateLimiter.tryAcquireWithResult("api:" + apiKey, 1, 
                                                       apiConfig.getWindowSizeSeconds(), 
                                                       apiConfig.getLimit());
        }
    }

    /**
     * 用户限流器
     */
    public static class UserRateLimiter {
        private final RedisRateLimiter redisRateLimiter;
        private final RedisRateLimiterConfig config;

        public UserRateLimiter(RedisRateLimiter redisRateLimiter, RedisRateLimiterConfig config) {
            this.redisRateLimiter = redisRateLimiter;
            this.config = config;
        }

        public boolean tryAcquire(String userId) {
            RedisRateLimiterConfig.RateLimiterConfigItem userConfig = config.getUserConfig();
            return redisRateLimiter.tryAcquire("user:" + userId, 1, 
                                             userConfig.getWindowSizeSeconds(), 
                                             userConfig.getLimit());
        }

        public RedisRateLimiter.RateLimitResult tryAcquireWithResult(String userId) {
            RedisRateLimiterConfig.RateLimiterConfigItem userConfig = config.getUserConfig();
            return redisRateLimiter.tryAcquireWithResult("user:" + userId, 1, 
                                                       userConfig.getWindowSizeSeconds(), 
                                                       userConfig.getLimit());
        }
    }

    /**
     * IP限流器
     */
    public static class IpRateLimiter {
        private final RedisRateLimiter redisRateLimiter;
        private final RedisRateLimiterConfig config;

        public IpRateLimiter(RedisRateLimiter redisRateLimiter, RedisRateLimiterConfig config) {
            this.redisRateLimiter = redisRateLimiter;
            this.config = config;
        }

        public boolean tryAcquire(String ip) {
            RedisRateLimiterConfig.RateLimiterConfigItem ipConfig = config.getIpConfig();
            return redisRateLimiter.tryAcquire("ip:" + ip, 1, 
                                             ipConfig.getWindowSizeSeconds(), 
                                             ipConfig.getLimit());
        }

        public RedisRateLimiter.RateLimitResult tryAcquireWithResult(String ip) {
            RedisRateLimiterConfig.RateLimiterConfigItem ipConfig = config.getIpConfig();
            return redisRateLimiter.tryAcquireWithResult("ip:" + ip, 1, 
                                                       ipConfig.getWindowSizeSeconds(), 
                                                       ipConfig.getLimit());
        }
    }

    /**
     * 订单限流器
     */
    public static class OrderRateLimiter {
        private final RedisRateLimiter redisRateLimiter;
        private final RedisRateLimiterConfig config;

        public OrderRateLimiter(RedisRateLimiter redisRateLimiter, RedisRateLimiterConfig config) {
            this.redisRateLimiter = redisRateLimiter;
            this.config = config;
        }

        public boolean tryAcquire(String orderKey) {
            RedisRateLimiterConfig.RateLimiterConfigItem orderConfig = config.getOrderConfig();
            return redisRateLimiter.tryAcquire("order:" + orderKey, 1, 
                                             orderConfig.getWindowSizeSeconds(), 
                                             orderConfig.getLimit());
        }

        public RedisRateLimiter.RateLimitResult tryAcquireWithResult(String orderKey) {
            RedisRateLimiterConfig.RateLimiterConfigItem orderConfig = config.getOrderConfig();
            return redisRateLimiter.tryAcquireWithResult("order:" + orderKey, 1, 
                                                       orderConfig.getWindowSizeSeconds(), 
                                                       orderConfig.getLimit());
        }
    }

    /**
     * 司机限流器
     */
    public static class DriverRateLimiter {
        private final RedisRateLimiter redisRateLimiter;
        private final RedisRateLimiterConfig config;

        public DriverRateLimiter(RedisRateLimiter redisRateLimiter, RedisRateLimiterConfig config) {
            this.redisRateLimiter = redisRateLimiter;
            this.config = config;
        }

        public boolean tryAcquire(String driverId) {
            RedisRateLimiterConfig.RateLimiterConfigItem driverConfig = config.getDriverConfig();
            return redisRateLimiter.tryAcquire("driver:" + driverId, 1, 
                                             driverConfig.getWindowSizeSeconds(), 
                                             driverConfig.getLimit());
        }

        public RedisRateLimiter.RateLimitResult tryAcquireWithResult(String driverId) {
            RedisRateLimiterConfig.RateLimiterConfigItem driverConfig = config.getDriverConfig();
            return redisRateLimiter.tryAcquireWithResult("driver:" + driverId, 1, 
                                                       driverConfig.getWindowSizeSeconds(), 
                                                       driverConfig.getLimit());
        }
    }

    /**
     * 调度限流器
     */
    public static class DispatchRateLimiter {
        private final RedisRateLimiter redisRateLimiter;
        private final RedisRateLimiterConfig config;

        public DispatchRateLimiter(RedisRateLimiter redisRateLimiter, RedisRateLimiterConfig config) {
            this.redisRateLimiter = redisRateLimiter;
            this.config = config;
        }

        public boolean tryAcquire(String dispatchKey) {
            RedisRateLimiterConfig.RateLimiterConfigItem dispatchConfig = config.getDispatchConfig();
            return redisRateLimiter.tryAcquire("dispatch:" + dispatchKey, 1, 
                                             dispatchConfig.getWindowSizeSeconds(), 
                                             dispatchConfig.getLimit());
        }

        public RedisRateLimiter.RateLimitResult tryAcquireWithResult(String dispatchKey) {
            RedisRateLimiterConfig.RateLimiterConfigItem dispatchConfig = config.getDispatchConfig();
            return redisRateLimiter.tryAcquireWithResult("dispatch:" + dispatchKey, 1, 
                                                       dispatchConfig.getWindowSizeSeconds(), 
                                                       dispatchConfig.getLimit());
        }
    }
}
