package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import com.ctrip.dcs.dsp.delay.limit.RedisRateLimiter;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Redis限流器使用示例
 * 展示如何在业务代码中使用Redis限流器
 * 
 * <AUTHOR>
 */
@Component
public class RedisRateLimiterExample {

    private static final Logger logger = LoggerFactory.getLogger(RedisRateLimiterExample.class);

    @Autowired
    @Qualifier("redisRateLimiter")
    private RedisRateLimiter redisRateLimiter;

    @Autowired
    @Qualifier("apiRateLimiter")
    private RedisRateLimiterConfiguration.ApiRateLimiter apiRateLimiter;

    @Autowired
    @Qualifier("userRateLimiter")
    private RedisRateLimiterConfiguration.UserRateLimiter userRateLimiter;

    @Autowired
    @Qualifier("ipRateLimiter")
    private RedisRateLimiterConfiguration.IpRateLimiter ipRateLimiter;

    @Autowired
    @Qualifier("orderRateLimiter")
    private RedisRateLimiterConfiguration.OrderRateLimiter orderRateLimiter;

    @Autowired
    @Qualifier("driverRateLimiter")
    private RedisRateLimiterConfiguration.DriverRateLimiter driverRateLimiter;

    @Autowired
    @Qualifier("dispatchRateLimiter")
    private RedisRateLimiterConfiguration.DispatchRateLimiter dispatchRateLimiter;

    /**
     * API限流示例
     */
    public boolean checkApiRateLimit(String apiKey) {
        try {
            boolean allowed = apiRateLimiter.tryAcquire(apiKey);
            if (!allowed) {
                logger.warn("RedisRateLimiterExample", "API rate limit exceeded for key: {}", apiKey);
            }
            return allowed;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "API rate limit check failed for key: {}, error: {}", apiKey, e.getMessage());
            return true; // 异常时允许通过
        }
    }

    /**
     * 用户限流示例
     */
    public boolean checkUserRateLimit(String userId) {
        try {
            RedisRateLimiter.RateLimitResult result = userRateLimiter.tryAcquireWithResult(userId);
            if (!result.isAllowed()) {
                logger.warn("RedisRateLimiterExample", "User rate limit exceeded for userId: {}, remaining: {}, resetTime: {}", 
                           userId, result.getRemaining(), result.getResetTime());
            }
            return result.isAllowed();
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "User rate limit check failed for userId: {}, error: {}", userId, e.getMessage());
            return true;
        }
    }

    /**
     * IP限流示例
     */
    public boolean checkIpRateLimit(String clientIp) {
        try {
            boolean allowed = ipRateLimiter.tryAcquire(clientIp);
            if (!allowed) {
                logger.warn("RedisRateLimiterExample", "IP rate limit exceeded for IP: {}", clientIp);
            }
            return allowed;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "IP rate limit check failed for IP: {}, error: {}", clientIp, e.getMessage());
            return true;
        }
    }

    /**
     * 订单限流示例
     */
    public boolean checkOrderRateLimit(String orderKey) {
        try {
            boolean allowed = orderRateLimiter.tryAcquire(orderKey);
            if (!allowed) {
                logger.warn("RedisRateLimiterExample", "Order rate limit exceeded for orderKey: {}", orderKey);
            }
            return allowed;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Order rate limit check failed for orderKey: {}, error: {}", orderKey, e.getMessage());
            return true;
        }
    }

    /**
     * 司机限流示例
     */
    public boolean checkDriverRateLimit(String driverId) {
        try {
            boolean allowed = driverRateLimiter.tryAcquire(driverId);
            if (!allowed) {
                logger.warn("RedisRateLimiterExample", "Driver rate limit exceeded for driverId: {}", driverId);
            }
            return allowed;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Driver rate limit check failed for driverId: {}, error: {}", driverId, e.getMessage());
            return true;
        }
    }

    /**
     * 调度限流示例
     */
    public boolean checkDispatchRateLimit(String dispatchKey) {
        try {
            RedisRateLimiter.RateLimitResult result = dispatchRateLimiter.tryAcquireWithResult(dispatchKey);
            logger.info("RedisRateLimiterExample", "Dispatch rate limit check for key: {}, allowed: {}, remaining: {}, currentCount: {}", 
                       dispatchKey, result.isAllowed(), result.getRemaining(), result.getCurrentCount());
            return result.isAllowed();
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Dispatch rate limit check failed for dispatchKey: {}, error: {}", dispatchKey, e.getMessage());
            return true;
        }
    }

    /**
     * 自定义限流示例
     */
    public boolean checkCustomRateLimit(String key, int windowSizeSeconds, int limit) {
        try {
            boolean allowed = redisRateLimiter.tryAcquire(key, 1, windowSizeSeconds, limit);
            if (!allowed) {
                logger.warn("RedisRateLimiterExample", "Custom rate limit exceeded for key: {}, window: {}s, limit: {}", 
                           key, windowSizeSeconds, limit);
            }
            return allowed;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Custom rate limit check failed for key: {}, error: {}", key, e.getMessage());
            return true;
        }
    }

    /**
     * 批量请求限流示例
     */
    public boolean checkBatchRateLimit(String key, int permits, int windowSizeSeconds, int limit) {
        try {
            RedisRateLimiter.RateLimitResult result = redisRateLimiter.tryAcquireWithResult(key, permits, windowSizeSeconds, limit);
            if (!result.isAllowed()) {
                logger.warn("RedisRateLimiterExample", "Batch rate limit exceeded for key: {}, permits: {}, remaining: {}", 
                           key, permits, result.getRemaining());
            }
            return result.isAllowed();
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Batch rate limit check failed for key: {}, permits: {}, error: {}", 
                        key, permits, e.getMessage());
            return true;
        }
    }

    /**
     * 获取限流状态信息
     */
    public void printRateLimitStatus(String key) {
        try {
            long remaining = redisRateLimiter.getRemaining(key);
            long currentCount = redisRateLimiter.getCurrentCount(key);
            long resetTime = redisRateLimiter.getResetTime(key);
            boolean available = redisRateLimiter.isAvailable();

            logger.info("RedisRateLimiterExample", "Rate limit status for key: {}, remaining: {}, currentCount: {}, resetTime: {}, available: {}", 
                       key, remaining, currentCount, resetTime, available);
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Failed to get rate limit status for key: {}, error: {}", key, e.getMessage());
        }
    }

    /**
     * 重置限流计数器
     */
    public boolean resetRateLimit(String key) {
        try {
            boolean result = redisRateLimiter.reset(key);
            logger.info("RedisRateLimiterExample", "Reset rate limit for key: {}, result: {}", key, result);
            return result;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Failed to reset rate limit for key: {}, error: {}", key, e.getMessage());
            return false;
        }
    }

    /**
     * 综合限流检查示例
     * 可以同时检查多个维度的限流
     */
    public boolean comprehensiveRateLimitCheck(String userId, String clientIp, String apiKey) {
        try {
            // 检查用户限流
            if (!checkUserRateLimit(userId)) {
                return false;
            }

            // 检查IP限流
            if (!checkIpRateLimit(clientIp)) {
                return false;
            }

            // 检查API限流
            if (!checkApiRateLimit(apiKey)) {
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.error("RedisRateLimiterExample", "Comprehensive rate limit check failed, userId: {}, IP: {}, apiKey: {}, error: {}", 
                        userId, clientIp, apiKey, e.getMessage());
            return true;
        }
    }
}
