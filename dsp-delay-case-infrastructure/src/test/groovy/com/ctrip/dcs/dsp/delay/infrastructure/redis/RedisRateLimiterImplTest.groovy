package com.ctrip.dcs.dsp.delay.infrastructure.redis

import com.ctrip.dcs.dsp.delay.limit.RedisRateLimiter
import com.ctrip.dcs.dsp.delay.qconfig.RedisRateLimiterConfig
import credis.java.client.CacheProvider
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static org.mockito.Mockito.*

/**
 * Redis限流器单元测试
 * <AUTHOR>
 */
class RedisRateLimiterImplTest extends Specification {
    
    @Mock
    CacheProvider cacheProvider
    
    @Mock
    RedisRateLimiterConfig config
    
    @InjectMocks
    RedisRateLimiterImpl redisRateLimiter

    def setup() {
        MockitoAnnotations.initMocks(this)
        
        // 设置默认配置
        when(config.getEnabled()).thenReturn(true)
        when(config.getKeyPrefix()).thenReturn("rate_limiter:")
        when(config.getLimiterType()).thenReturn("sliding_window")
        when(config.getSlidingWindowShards()).thenReturn(10)
        when(config.getDebugMode()).thenReturn(false)
        
        def defaultConfig = new RedisRateLimiterConfig.RateLimiterConfigItem(60, 100, 120)
        when(config.getDefaultConfig()).thenReturn(defaultConfig)
    }

    def "test tryAcquire with disabled limiter should always return true"() {
        given:
        when(config.getEnabled()).thenReturn(false)
        
        when:
        boolean result = redisRateLimiter.tryAcquire("test_key", 1)
        
        then:
        result == true
        verifyNoInteractions(cacheProvider)
    }

    def "test tryAcquire with sliding window - first request should succeed"() {
        given:
        String key = "test_key"
        int permits = 1
        int windowSize = 60
        int limit = 100
        
        // Mock Redis responses for sliding window calculation
        when(cacheProvider.get(anyString())).thenReturn(null)
        when(cacheProvider.incr(anyString(), anyInt())).thenReturn(1L)
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, permits, windowSize, limit)
        
        then:
        result == true
        verify(cacheProvider, atLeastOnce()).incr(anyString(), eq(permits))
        verify(cacheProvider, atLeastOnce()).expire(anyString(), anyInt())
    }

    def "test tryAcquire with sliding window - should reject when limit exceeded"() {
        given:
        String key = "test_key"
        int permits = 1
        int windowSize = 60
        int limit = 5
        
        // Mock Redis responses to simulate limit exceeded
        when(cacheProvider.get(anyString())).thenReturn("5") // Current count is at limit
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, permits, windowSize, limit)
        
        then:
        result == false
        verify(cacheProvider, never()).incr(anyString(), anyInt())
    }

    def "test tryAcquire with fixed window"() {
        given:
        String key = "test_key"
        int permits = 1
        int windowSize = 60
        int limit = 100
        
        when(config.getLimiterType()).thenReturn("fixed_window")
        when(cacheProvider.get(anyString())).thenReturn("50") // Current count
        when(cacheProvider.incr(anyString(), anyInt())).thenReturn(51L)
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, permits, windowSize, limit)
        
        then:
        result == true
        verify(cacheProvider).incr(anyString(), eq(permits))
    }

    def "test tryAcquireWithResult returns correct information"() {
        given:
        String key = "test_key"
        int permits = 1
        int windowSize = 60
        int limit = 100
        
        when(cacheProvider.get(anyString())).thenReturn("10") // Current count
        when(cacheProvider.incr(anyString(), anyInt())).thenReturn(11L)
        
        when:
        RedisRateLimiter.RateLimitResult result = redisRateLimiter.tryAcquireWithResult(key, permits, windowSize, limit)
        
        then:
        result.isAllowed() == true
        result.getRemaining() == 89 // 100 - 10 - 1
        result.getCurrentCount() == 11
        result.getResetTime() > 0
    }

    def "test getRemaining"() {
        given:
        String key = "test_key"
        
        when(cacheProvider.get(anyString())).thenReturn("30") // Current count
        
        when:
        long remaining = redisRateLimiter.getRemaining(key, 60, 100)
        
        then:
        remaining == 70 // 100 - 30
    }

    def "test getCurrentCount"() {
        given:
        String key = "test_key"
        
        when(cacheProvider.get(anyString())).thenReturn("25") // Current count
        
        when:
        long currentCount = redisRateLimiter.getCurrentCount(key, 60)
        
        then:
        currentCount == 25
    }

    def "test isAvailable with healthy Redis"() {
        given:
        when(cacheProvider.set(anyString(), anyString())).thenReturn(true)
        when(cacheProvider.get(anyString())).thenReturn("1")
        
        when:
        boolean available = redisRateLimiter.isAvailable()
        
        then:
        available == true
    }

    def "test isAvailable with unhealthy Redis"() {
        given:
        when(cacheProvider.set(anyString(), anyString())).thenThrow(new RuntimeException("Redis error"))
        
        when:
        boolean available = redisRateLimiter.isAvailable()
        
        then:
        available == false
    }

    def "test getResetTime for sliding window"() {
        given:
        String key = "test_key"
        int windowSize = 60
        long currentTime = System.currentTimeMillis() / 1000
        
        when:
        long resetTime = redisRateLimiter.getResetTime(key, windowSize)
        
        then:
        resetTime >= currentTime + windowSize - 1
        resetTime <= currentTime + windowSize + 1
    }

    def "test getResetTime for fixed window"() {
        given:
        String key = "test_key"
        int windowSize = 60
        long currentTime = System.currentTimeMillis() / 1000
        long expectedWindowStart = (currentTime / windowSize) * windowSize
        
        when(config.getLimiterType()).thenReturn("fixed_window")
        
        when:
        long resetTime = redisRateLimiter.getResetTime(key, windowSize)
        
        then:
        resetTime == expectedWindowStart + windowSize
    }

    def "test error handling - Redis exception should allow request"() {
        given:
        String key = "test_key"
        when(cacheProvider.get(anyString())).thenThrow(new RuntimeException("Redis connection error"))
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, 1, 60, 100)
        
        then:
        result == true // Should allow request when Redis fails
    }

    def "test multiple permits request"() {
        given:
        String key = "test_key"
        int permits = 5
        int windowSize = 60
        int limit = 100
        
        when(cacheProvider.get(anyString())).thenReturn("90") // Current count
        when(cacheProvider.incr(anyString(), anyInt())).thenReturn(95L)
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, permits, windowSize, limit)
        
        then:
        result == true
        verify(cacheProvider).incr(anyString(), eq(permits))
    }

    def "test multiple permits request exceeding limit"() {
        given:
        String key = "test_key"
        int permits = 10
        int windowSize = 60
        int limit = 100
        
        when(cacheProvider.get(anyString())).thenReturn("95") // Current count
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, permits, windowSize, limit)
        
        then:
        result == false // 95 + 10 > 100
        verify(cacheProvider, never()).incr(anyString(), anyInt())
    }

    def "test reset functionality"() {
        given:
        String key = "test_key"
        
        when:
        boolean result = redisRateLimiter.reset(key)
        
        then:
        result == true // Simplified implementation always returns true
    }

    def "test invalid count value handling"() {
        given:
        String key = "test_key"
        when(cacheProvider.get(anyString())).thenReturn("invalid_number")
        when(cacheProvider.incr(anyString(), anyInt())).thenReturn(1L)
        
        when:
        boolean result = redisRateLimiter.tryAcquire(key, 1, 60, 100)
        
        then:
        result == true // Should handle invalid count gracefully
    }
}
