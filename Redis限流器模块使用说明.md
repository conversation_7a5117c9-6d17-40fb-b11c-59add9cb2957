# Redis限流器模块使用说明

## 概述

本模块提供了一个基于Redis的分布式限流器，支持滑动窗口和固定窗口两种限流算法。所有限流参数都通过配置文件进行配置，不使用lua脚本，采用纯Java实现。

## 特性

- **分布式限流**: 基于Redis实现，支持多实例部署
- **多种算法**: 支持滑动窗口和固定窗口限流算法
- **配置驱动**: 所有参数通过配置文件配置，支持动态调整
- **多维度限流**: 支持API、用户、IP、订单、司机、调度等多种限流维度
- **异常容错**: Redis异常时自动降级，不影响业务正常运行
- **详细监控**: 提供丰富的限流状态信息和调试功能

## 架构设计

```
├── domain层
│   ├── limit/RedisRateLimiter.java          # 限流器接口
│   └── qconfig/RedisRateLimiterConfig.java  # 配置类
├── infrastructure层
│   ├── redis/RedisRateLimiterImpl.java           # 限流器实现
│   ├── redis/RedisRateLimiterConfiguration.java # Spring配置
│   └── redis/RedisRateLimiterExample.java        # 使用示例
└── 配置文件
    └── redis_rate_limiter.properties        # 限流配置
```

## 配置说明

### 基础配置

```properties
# 是否启用Redis限流器
enabled=true

# 限流器类型：sliding_window（滑动窗口）或 fixed_window（固定窗口）
limiterType=sliding_window

# Redis key前缀
keyPrefix=rate_limiter:

# 是否启用调试模式
debugMode=false
```

### 限流配置

每种限流类型都有三个参数：
- `WindowSizeSeconds`: 窗口大小（秒）
- `Limit`: 限流阈值（次/窗口）
- `ExpireSeconds`: 过期时间（秒）

```properties
# API限流配置
apiWindowSizeSeconds=60
apiLimit=1000
apiExpireSeconds=120

# 用户限流配置
userWindowSizeSeconds=60
userLimit=100
userExpireSeconds=120

# IP限流配置
ipWindowSizeSeconds=60
ipLimit=500
ipExpireSeconds=120

# 订单限流配置
orderWindowSizeSeconds=60
orderLimit=200
orderExpireSeconds=120

# 司机限流配置
driverWindowSizeSeconds=60
driverLimit=50
driverExpireSeconds=120

# 调度限流配置
dispatchWindowSizeSeconds=60
dispatchLimit=300
dispatchExpireSeconds=120
```

## 使用方法

### 1. 基本使用

```java
@Autowired
@Qualifier("redisRateLimiter")
private RedisRateLimiter redisRateLimiter;

// 简单限流检查
boolean allowed = redisRateLimiter.tryAcquire("user:123");

// 自定义参数限流检查
boolean allowed = redisRateLimiter.tryAcquire("custom_key", 1, 60, 100);
```

### 2. 使用专用限流器

```java
@Autowired
@Qualifier("apiRateLimiter")
private RedisRateLimiterConfiguration.ApiRateLimiter apiRateLimiter;

@Autowired
@Qualifier("userRateLimiter")
private RedisRateLimiterConfiguration.UserRateLimiter userRateLimiter;

// API限流
boolean allowed = apiRateLimiter.tryAcquire("api_key");

// 用户限流
boolean allowed = userRateLimiter.tryAcquire("user_id");
```

### 3. 获取详细结果

```java
RedisRateLimiter.RateLimitResult result = redisRateLimiter.tryAcquireWithResult("key", 1);

if (result.isAllowed()) {
    // 请求被允许
    System.out.println("剩余次数: " + result.getRemaining());
    System.out.println("当前计数: " + result.getCurrentCount());
    System.out.println("重置时间: " + result.getResetTime());
} else {
    // 请求被限流
    System.out.println("请求被限流，请稍后重试");
}
```

### 4. 批量请求限流

```java
// 一次请求消耗5个许可
boolean allowed = redisRateLimiter.tryAcquire("batch_key", 5, 60, 100);
```

### 5. 获取限流状态

```java
// 获取剩余许可数
long remaining = redisRateLimiter.getRemaining("key");

// 获取当前计数
long currentCount = redisRateLimiter.getCurrentCount("key");

// 获取重置时间
long resetTime = redisRateLimiter.getResetTime("key");

// 检查限流器是否可用
boolean available = redisRateLimiter.isAvailable();
```

### 6. 重置限流计数

```java
// 重置指定key的限流计数
boolean success = redisRateLimiter.reset("key");
```

## 算法说明

### 滑动窗口算法

滑动窗口算法将时间窗口分成多个小分片，通过统计窗口内所有分片的请求总数来判断是否超过限制。

**优点**:
- 限流更加平滑，避免突发流量
- 更精确的流量控制

**缺点**:
- 实现复杂度较高
- Redis存储开销较大

### 固定窗口算法

固定窗口算法将时间划分为固定大小的窗口，每个窗口内独立计数。

**优点**:
- 实现简单
- Redis存储开销小

**缺点**:
- 可能出现窗口边界突发流量问题

## 监控和调试

### 启用调试模式

```properties
debugMode=true
```

启用后会输出详细的限流日志，包括：
- 限流key
- 请求许可数
- 当前计数
- 限流阈值
- 是否允许
- 剩余许可数

### 健康检查

```java
boolean healthy = redisRateLimiter.isAvailable();
```

## 异常处理

当Redis出现异常时，限流器会自动降级：
- `tryAcquire` 方法返回 `true`（允许请求通过）
- `getRemaining` 方法返回配置的最大限制值
- `getCurrentCount` 方法返回 `0`

这样设计是为了确保Redis故障不会影响业务的正常运行。

## 性能考虑

1. **Redis连接池**: 确保Redis连接池配置合理
2. **网络延迟**: 限流检查会增加网络往返时间
3. **并发性能**: 滑动窗口算法在高并发下性能略低于固定窗口
4. **内存使用**: 滑动窗口算法会使用更多Redis内存

## 最佳实践

1. **合理设置窗口大小**: 根据业务特点选择合适的窗口大小
2. **设置合理的过期时间**: 过期时间应该大于窗口大小，建议设置为窗口大小的2倍
3. **监控限流效果**: 定期检查限流日志，调整限流参数
4. **异常处理**: 在业务代码中妥善处理限流异常
5. **测试验证**: 在生产环境部署前充分测试限流效果

## 注意事项

1. 本实现不使用lua脚本，在极高并发场景下可能存在轻微的计数不准确问题
2. 滑动窗口算法的分片数量会影响精度和性能，需要根据实际情况调整
3. Redis时钟与应用服务器时钟应保持同步
4. 建议在生产环境中启用Redis持久化，避免重启后限流数据丢失

## 扩展功能

如需扩展功能，可以：
1. 实现自定义限流算法
2. 添加更多限流维度
3. 集成监控系统
4. 实现限流规则的动态配置
