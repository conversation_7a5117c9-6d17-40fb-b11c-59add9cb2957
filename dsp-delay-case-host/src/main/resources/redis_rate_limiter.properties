# Redis???????
# ??????????????????????lua??

# ????
# Redis key??
keyPrefix=rate_limiter:

# ????Redis???
enabled=true

# ????????
debugMode=false

# ??????sliding_window??????? fixed_window??????
limiterType=sliding_window

# ????????
slidingWindowShards=10

# Redis??????????
redisTimeoutMs=1000

# ??????
# ?????????
defaultWindowSizeSeconds=60

# ??????
defaultLimit=100

# ?????????
defaultExpireSeconds=120

# API????
# API?????????
apiWindowSizeSeconds=60

# API??????/???
apiLimit=1000

# API?????????
apiExpireSeconds=120

# ??????
# ???????????
userWindowSizeSeconds=60

# ????????/???
userLimit=100

# ???????????
userExpireSeconds=120

# IP????
# IP?????????
ipWindowSizeSeconds=60

# IP??????/???
ipLimit=500

# IP?????????
ipExpireSeconds=120

# ??????
# ???????????
orderWindowSizeSeconds=60

# ????????/???
orderLimit=200

# ???????????
orderExpireSeconds=120

# ??????
# ???????????
driverWindowSizeSeconds=60

# ????????/???
driverLimit=50

# ???????????
driverExpireSeconds=120

# ??????
# ???????????
dispatchWindowSizeSeconds=60

# ????????/???
dispatchLimit=300

# ???????????
dispatchExpireSeconds=120
